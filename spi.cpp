#include <SPI.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_MOSI   23
#define OLED_CLK    18
#define OLED_DC     15
#define OLED_CS     5
#define OLED_RESET  4

#define CS_TC 26

Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT,
  OLED_MOSI, OLED_CLK, OLED_DC, OLED_RESET, OLED_CS);

SPISettings spiMAX(4000000, MSBFIRST, SPI_MODE0);

float readMAX6675() {
  uint16_t high;
  SPI.beginTransaction(spiMAX);
  digitalWrite(CS_TC, LOW);
  high = SPI.transfer16(0x00);
  digitalWrite(CS_TC, HIGH);
  SPI.endTransaction();

  if (high & 0x0004) return NAN;
  high >>= 3;
  return high * 0.25;
}

void setup() {
  Serial.begin(115200);
  Serial.println("Starting setup...");

  // Initialize SPI first
  SPI.begin();
  Serial.println("SPI initialized");

  // Setup thermocouple pins
  pinMode(CS_TC, OUTPUT);
  digitalWrite(CS_TC, HIGH);
  Serial.println("Thermocouple pins configured");

  // Initialize display
  Serial.println("Initializing display...");
  if(!display.begin(SSD1306_SWITCHCAPVCC)) {
    Serial.println("SSD1306 allocation failed");
    for(;;); // Don't proceed, loop forever
  }
  Serial.println("Display initialized successfully");

  display.clearDisplay();
  display.setTextSize(2);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(0, 0);
  display.println("Thermo");
  display.display();
  Serial.println("Setup complete, entering loop...");
}

void loop() {
  Serial.println("Loop started");
  float tempC = readMAX6675();

  display.clearDisplay();
  display.setTextSize(2);
  display.setCursor(0, 0);  // Fixed: removed comment, set cursor properly
  display.println("Temp:");
  display.setCursor(0, 24);
  if (isnan(tempC)) {
    display.println("Fault");
  } else {
    display.print(tempC, 1);
    display.println(" C");
  }
  display.display();

  Serial.print("Temperature: ");
  if (isnan(tempC)) {
    Serial.println("Thermo Fault");
  } else {
    Serial.print(tempC, 1);
    Serial.println(" C");
  }

  delay(1000);
}