#include <SPI.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_MOSI   23
#define OLED_CLK    18
#define OLED_DC     15
#define OLED_CS     5
#define OLED_RESET  4

#define CS_TC 26

Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT,
  OLED_MOSI, OLED_CLK, OLED_DC, OLED_RESET, OLED_CS);

SPISettings spiMAX(4000000, MSBFIRST, SPI_MODE0);

float readMAX6675() {
  uint16_t high;
  SPI.beginTransaction(spiMAX);
  digitalWrite(CS_TC, LOW);
  high = SPI.transfer16(0x00);
  digitalWrite(CS_TC, HIGH);
  SPI.endTransaction();

  if (high & 0x0004) return NAN;
  high >>= 3;
  return high * 0.25;
}

void setup() {
  Serial.begin(115200);
  pinMode(CS_TC, OUTPUT);
  digitalWrite(CS_TC, HIGH);

  display.begin(SSD1306_SWITCHCAPVCC);
  display.clearDisplay();
  display.setTextSize(2);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(0, 0);
  display.println("Thermo");
  display.display();

  SPI.begin();
    
}

void loop() {
  float tempC = readMAX6675();

  display.clearDisplay();
  display.setTextSize(2);
//  display.setCursor(0, 0);
  display.print("Thermoscouple");
  display.setCursor(0, 24);
  if (isnan(tempC)) {
    display.println("Thermo Fault");
  } else {
    display.print(tempC, 1);
    display.println(" C");
  }
  display.display();

  Serial.print("Temperature: ");
  if (isnan(tempC)) {
    Serial.println("Thermo Fault");
  } else {
    Serial.print(tempC, 1);
    Serial.println(" C");
  }

  delay(1000);
}